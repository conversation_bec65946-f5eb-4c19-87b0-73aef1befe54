<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('supplies', function (Blueprint $table) {
            // Ajouter product_id après category_id
            $table->foreignId('product_id')->nullable()->after('category_id')->constrained('products')->onDelete('cascade');

            // Ajouter region_id après product_id
            $table->foreignId('region_id')->nullable()->after('product_id')->constrained('regions')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('supplies', function (Blueprint $table) {
            // Supprimer les contraintes de clé étrangère et les colonnes
            $table->dropForeign(['product_id']);
            $table->dropForeign(['region_id']);
            $table->dropColumn(['product_id', 'region_id']);
        });
    }
};
