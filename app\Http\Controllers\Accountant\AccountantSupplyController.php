<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Product;
use App\Models\Region;
use App\Models\City;
use App\Models\Truck;
use App\Models\Supply;
use App\Models\SupplyDetail;
use App\Models\Driver;
use App\Models\Supplier;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use App\Models\StockHistory;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use Carbon\Carbon;
use DB;

class AccountantSupplyController extends Controller
{
    /**
     * Récupère les statistiques des approvisionnements du comptable connecté
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMySupplies(Request $request)
    {
        try {
            $accountantId = Auth::id();
            
            // Log pour débogage
            Log::info('Début getMySupplies', [
                'accountant_id' => $accountantId,
                'request' => $request->all()
            ]);
            
            // Période par défaut (7 jours) ou personnalisée
            $period = $request->input('period', 7);
            $endDate = Carbon::now();
            $startDate = Carbon::now()->subDays($period);
            
            Log::info('Période sélectionnée', [
                'period' => $period,
                'startDate' => $startDate->toDateTimeString(),
                'endDate' => $endDate->toDateTimeString()
            ]);
            
            // Vérifier si la table supplies existe et contient des données
            $suppliesTableExists = Schema::hasTable('supplies');
            Log::info('Vérification de la table supplies', [
                'table_exists' => $suppliesTableExists
            ]);
            
            if (!$suppliesTableExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'La table supplies n\'existe pas',
                    'error' => 'Table not found'
                ], 500);
            }
            
            // Vérifier si la colonne created_by existe
            $columns = Schema::getColumnListing('supplies');
            Log::info('Colonnes de la table supplies', [
                'columns' => $columns,
                'has_created_by' => in_array('created_by', $columns)
            ]);
            
            if (!in_array('created_by', $columns)) {
                return response()->json([
                    'success' => false,
                    'message' => 'La colonne created_by n\'existe pas dans la table supplies',
                    'error' => 'Column not found'
                ], 500);
            }
            
            // Récupérer les approvisionnements du comptable connecté
            $query = Supply::where('created_by', $accountantId);
            if ($period > 0) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }
            $mySuppliesCount = $query->count();
            
            Log::info('Approvisionnements du comptable', [
                'accountant_id' => $accountantId,
                'count' => $mySuppliesCount,
                'sql' => $query->toSql(),
                'bindings' => $query->getBindings()
            ]);
            
            // Récupérer le total des approvisionnements pour calculer le pourcentage
            $totalQuery = Supply::query();
            if ($period > 0) {
                $totalQuery->whereBetween('created_at', [$startDate, $endDate]);
            }
            $totalSupplies = $totalQuery->count();
            
            Log::info('Total des approvisionnements', [
                'count' => $totalSupplies,
                'sql' => $totalQuery->toSql(),
                'bindings' => $totalQuery->getBindings()
            ]);
            
            // Calculer le pourcentage
            $percentage = ($totalSupplies > 0) ? round(($mySuppliesCount / $totalSupplies) * 100) : 0;
            
            // Si aucun approvisionnement n'est trouvé, renvoyer des données de test
            if ($mySuppliesCount == 0 && $totalSupplies == 0) {
                $response = [
                    'success' => true,
                    'mySupplies' => 5, // Valeur de test
                    'totalSupplies' => 20, // Valeur de test
                    'percentage' => 25, // Valeur de test
                    'isTestData' => true // Indicateur que ce sont des données de test
                ];
            } else {
                $response = [
                    'success' => true,
                    'mySupplies' => $mySuppliesCount,
                    'totalSupplies' => $totalSupplies,
                    'percentage' => $percentage
                ];
            }
            
            Log::info('Réponse getMySupplies', $response);
            
            return response()->json($response);
            
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des approvisionnements du comptable', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de la récupération des approvisionnements.',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Affiche la liste des approvisionnements
     */
    public function index()
    {
        try {
            $supplies = Supply::with(['createdBy', 'supplier'])
                ->orderBy('created_at', 'desc')
                ->paginate(10);

            // Calcul des statistiques de tonnage par statut
            $totalTonnage = Supply::sum('total_tonnage') ?? 0;
            $pendingTonnage = Supply::where('status', 'pending')->sum('total_tonnage') ?? 0;
            $validatedTonnage = Supply::where('status', 'validated')->sum('total_tonnage') ?? 0;
            $rejectedTonnage = Supply::where('status', 'rejected')->sum('total_tonnage') ?? 0;

            // Statistiques pour la vue
            $stats = [
                'totalTonnage' => $totalTonnage,
                'pendingTonnage' => $pendingTonnage,
                'validatedTonnage' => $validatedTonnage,
                'rejectedTonnage' => $rejectedTonnage
            ];

            return view('accountant.supplies.index', compact('supplies', 'stats'));
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des approvisionnements', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return view('accountant.supplies.index', [
                'error' => 'Une erreur est survenue lors de la récupération des approvisionnements',
                'supplies' => collect(),
                'stats' => [
                    'totalTonnage' => 0,
                    'pendingTonnage' => 0,
                    'validatedTonnage' => 0,
                    'rejectedTonnage' => 0
                ]
            ]);
        }
    }

    /**
     * Affiche le formulaire de création d'un approvisionnement
     */
    public function create()
    {
        try {
            \Log::info('Début de la méthode create');
            
            // Récupérer les catégories
            $categories = Category::where('is_active', true)->get();
            
            \Log::info('Catégories récupérées:', [
                'count' => $categories->count(),
                'categories' => $categories->toArray()
            ]);
            
            // Récupérer les régions et fournisseurs
            $regions = Region::all();
            $suppliers = Supplier::all();
            
            // Vérifier que les données sont bien passées à la vue
            $viewData = [
                'categories' => $categories,
                'regions' => $regions,
                'suppliers' => $suppliers
            ];
            
            \Log::info('Données passées à la vue:', [
                'categories_count' => $viewData['categories']->count(),
                'regions_count' => $viewData['regions']->count(),
                'suppliers_count' => $viewData['suppliers']->count()
            ]);
            
            return view('accountant.supplies.create', $viewData);
            
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la récupération des données:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            return view('accountant.supplies.create', [
                'categories' => collect([]),
                'regions' => collect([]),
                'suppliers' => collect([]),
                'error' => 'Erreur: ' . $e->getMessage()
            ])->with('error', $e->getMessage());
        }
    }

    /**
     * Récupère les produits d'une catégorie
     */
    public function getProductsByCategory($categoryId)
    {
        try {
            $products = Product::where('category_id', $categoryId)
                ->where('is_active', true)
                ->select('id', 'name')
                ->get();

            return response()->json([
                'success' => true,
                'products' => $products
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Récupère les villes d'une région
     */
    public function getCitiesByRegion($regionId)
    {
        try {
            $cities = City::where('region_id', $regionId)
                ->where('is_active', true)
                ->get(['id', 'name']);

            return response()->json([
                'success' => true,
                'cities' => $cities
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des villes', [
                'error' => $e->getMessage(),
                'region_id' => $regionId,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de la récupération des villes'
            ], 500);
        }
    }

    /**
     * Récupère la liste des camions disponibles
     */
    public function getTrucks()
    {
        try {
            // Récupérer uniquement les camions qui ont un chauffeur assigné et le statut "assigned"
            $trucks = Truck::with(['driver', 'capacity'])
                ->whereHas('driver', function($query) {
                    $query->whereNotNull('id');
                })
                ->where('status', Truck::STATUS_ASSIGNED)
                ->get();

            $formattedTrucks = $trucks->map(function ($truck) {
                return [
                    'id' => $truck->id,
                    'registration_number' => $truck->registration_number,
                    'driver' => [
                        'id' => $truck->driver->id ?? null,
                        'first_name' => $truck->driver ? trim($truck->driver->first_name) : '',
                        'last_name' => $truck->driver ? trim($truck->driver->last_name) : ''
                    ],
                    'capacity' => $truck->capacity ? $truck->capacity->capacity . ' ' . $truck->capacity->unit : 'Non définie'
                ];
            });

            return response()->json([
                'success' => true,
                'trucks' => $formattedTrucks
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des camions', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de la récupération des camions'
            ], 500);
        }
    }

    /**
     * Enregistre un nouvel approvisionnement
     */
    public function store(Request $request)
    {
        try {
            Log::info('Début de l\'enregistrement d\'un nouvel approvisionnement', [
                'request' => $request->all()
            ]);

            // Récupérer la catégorie pour déterminer le type
            $category = Category::find($request->category_id);
            $isCimentCategory = $category && stripos($category->name, 'ciment') !== false;

            // Validation conditionnelle selon la catégorie
            $validationRules = [
                'product_id' => 'required|exists:products,id',
                'category_id' => 'required|exists:categories,id',
                'cities' => 'required|array',
                'cities.*.id' => 'required|exists:cities,id',
                'cities.*.quantity' => 'required|numeric|min:0',
                'cities.*.price' => 'required|numeric|min:0',
                'cities.*.vehicle_id' => 'required|exists:trucks,id',
                'cities.*.driver_id' => 'required|exists:drivers,id',
                'cities.*.trips' => 'required|integer|min:1',
                'date' => 'required|date',
                'notes' => 'nullable|string',
                'reference' => 'required|string|unique:supplies,reference',
                'supplier_id' => 'required|exists:suppliers,id'
            ];

            // Ajouter les règles conditionnelles
            if ($isCimentCategory) {
                $validationRules['invoice_file'] = 'required|file|mimes:jpeg,jpg,png,pdf|max:5120'; // 5MB max
            } else {
                $validationRules['expected_delivery_date'] = 'required|date|after_or_equal:date';
            }

            $validated = $request->validate($validationRules, [
                'cities.*.driver_id.required' => 'Un chauffeur doit être assigné à chaque camion',
                'cities.*.driver_id.exists' => 'Le chauffeur sélectionné n\'existe pas',
                'cities.*.vehicle_id.required' => 'Un véhicule doit être sélectionné pour chaque ville',
                'cities.*.vehicle_id.exists' => 'Le véhicule sélectionné n\'existe pas',
                'cities.*.quantity.required' => 'La quantité est requise pour chaque ville',
                'cities.*.quantity.numeric' => 'La quantité doit être un nombre',
                'cities.*.quantity.min' => 'La quantité doit être supérieure à 0',
                'cities.*.price.required' => 'Le prix est requis pour chaque ville',
                'cities.*.price.numeric' => 'Le prix doit être un nombre',
                'cities.*.price.min' => 'Le prix doit être supérieur à 0',
                'cities.*.trips.required' => 'Le nombre de voyages est requis pour chaque ville',
                'cities.*.trips.integer' => 'Le nombre de voyages doit être un nombre entier',
                'cities.*.trips.min' => 'Le nombre de voyages doit être au moins 1',
                'invoice_file.required' => 'La facture d\'approvisionnement est requise pour le ciment',
                'invoice_file.file' => 'Le fichier de facture doit être un fichier valide',
                'invoice_file.mimes' => 'La facture doit être au format JPG, PNG ou PDF',
                'invoice_file.max' => 'La taille de la facture ne doit pas dépasser 5MB'
            ]);

            // Calculer le tonnage total et le montant total
            $totalTonnage = collect($validated['cities'])->sum('quantity');
            $totalAmount = collect($validated['cities'])->sum(function ($city) {
                return $city['quantity'] * $city['price'];
            });

            // Gérer le fichier de facture pour la catégorie ciment
            $invoiceFilePath = null;
            if ($isCimentCategory && $request->hasFile('invoice_file')) {
                $file = $request->file('invoice_file');
                $fileName = time() . '_' . $validated['reference'] . '.' . $file->getClientOriginalExtension();

                // Stocker dans le dossier public/uploads/avatars/documents
                $destinationPath = public_path('uploads/avatars/documents');

                // Créer le dossier s'il n'existe pas
                if (!file_exists($destinationPath)) {
                    mkdir($destinationPath, 0755, true);
                }

                // Déplacer le fichier vers le dossier de destination
                $file->move($destinationPath, $fileName);

                // Stocker le chemin relatif pour la base de données
                $invoiceFilePath = 'uploads/avatars/documents/' . $fileName;
            }

            // Créer l'approvisionnement
            DB::beginTransaction();
            try {
                $supplyData = [
                    'reference' => $validated['reference'],
                    'supplier_id' => $validated['supplier_id'],
                    'category_id' => $validated['category_id'],
                    'date' => $validated['date'],
                    'notes' => $validated['notes'] ?? null,
                    'total_tonnage' => $totalTonnage,
                    'total_remaining' => $totalTonnage,
                    'total_amount' => $totalAmount,
                    'status' => 'pending',
                    'created_by' => auth()->id()
                ];

                // Ajouter conditionnellement les champs selon la catégorie
                if ($isCimentCategory) {
                    $supplyData['invoice_file'] = $invoiceFilePath;
                    // Pour le ciment, on peut mettre la date de livraison à la date d'approvisionnement
                    $supplyData['expected_delivery_date'] = $validated['date'];
                } else {
                    $supplyData['expected_delivery_date'] = $validated['expected_delivery_date'];
                }

                $supply = Supply::create($supplyData);

                // Créer le détail de l'approvisionnement
                $detail = new SupplyDetail([
                    'product_id' => $validated['product_id'],
                    'region_id' => $validated['region_id'],
                    'quantity' => $totalTonnage,
                    'tonnage' => $totalTonnage,
                    'unit_price' => collect($validated['cities'])->avg('price'),
                    'total_price' => $totalAmount,
                    'status' => 'pending'
                ]);
                $supply->details()->save($detail);

                // Enregistrer les détails pour chaque ville
                $supplyDetails = [];
                foreach ($validated['cities'] as $cityData) {
                    $supplyDetails[] = [
                        'city_id' => $cityData['id'],
                        'quantity' => $cityData['quantity'],
                        'price' => $cityData['price'],
                        'vehicle_id' => $cityData['vehicle_id'],
                        'driver_id' => $cityData['driver_id'],
                        'trips' => $cityData['trips']
                    ];
                }
                $supply->cities()->createMany($supplyDetails);

                DB::commit();

                Log::info('Approvisionnement créé avec succès', [
                    'supply_id' => $supply->id,
                    'product_id' => $validated['product_id'],
                    'total_tonnage' => $totalTonnage,
                    'total_amount' => $totalAmount,
                    'detail_id' => $detail->id,
                    'cities_count' => count($supplyDetails)
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Approvisionnement créé avec succès',
                    'redirect' => route('accountant.supplies.index')
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Erreur lors de la création de l\'approvisionnement', [
                    'error' => $e->getMessage(),
                    'request' => $request->all(),
                    'trace' => $e->getTraceAsString()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Une erreur est survenue lors de la création de l\'approvisionnement : ' . $e->getMessage()
                ], 422);
            }

        } catch (\Exception $e) {
            Log::error('Erreur lors de la création de l\'approvisionnement', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de la création de l\'approvisionnement : ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Affiche les détails d'un approvisionnement
     */
    public function show(Supply $supply)
    {
        try {
            $supply->load([
                'supplier',
                'createdBy',
                'validator',
                'details.product.category',
                'cities.city',
                'cities.vehicle.capacity',
                'cities.driver'
            ]);

            // Déboguer les détails de l'approvisionnement
            Log::info('Détails de l\'approvisionnement', [
                'supply_id' => $supply->id,
                'details_count' => $supply->details->count(),
                'details' => $supply->details->toArray(),
                'cities_count' => $supply->cities->count(),
                'cities' => $supply->cities->toArray()
            ]);

            return view('accountant.supplies.show', compact('supply'));
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des détails de l\'approvisionnement', [
                'error' => $e->getMessage(),
                'supply_id' => $supply->id,
                'trace' => $e->getTraceAsString()
            ]);

            return view('accountant.supplies.show', [
                'error' => 'Une erreur est survenue lors de la récupération des détails de l\'approvisionnement'
            ]);
        }
    }

    /**
     * Affiche le formulaire de modification d'un approvisionnement
     */
    public function edit(Supply $supply)
    {
        try {
            $categories = Category::where('is_active', true)->get();
            $regions = Region::all();
            $supply->load(['product', 'region', 'cities', 'trucks']);
            
            return view('accountant.supplies.edit', compact('supply', 'categories', 'regions'));
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des catégories et régions pour la modification de l\'approvisionnement', [
                'error' => $e->getMessage(),
                'supply_id' => $supply->id,
                'trace' => $e->getTraceAsString()
            ]);

            return view('accountant.supplies.edit', [
                'error' => 'Une erreur est survenue lors de la récupération des catégories et régions'
            ]);
        }
    }

    /**
     * Met à jour un approvisionnement
     */
    public function update(Request $request, Supply $supply)
    {
        try {
            Log::info('Début de la mise à jour de l\'approvisionnement', [
                'supply_id' => $supply->id,
                'request' => $request->all()
            ]);

            // Validation des données
            $validated = $request->validate([
                'product_id' => 'required|exists:products,id',
                'cities' => 'required|array',
                'cities.*.id' => 'required|exists:cities,id',
                'cities.*.quantity' => 'required|numeric|min:0',
                'cities.*.price' => 'required|numeric|min:0',
                'cities.*.vehicle_id' => 'required|exists:trucks,id',
                'cities.*.driver_id' => 'required|exists:drivers,id',
                'cities.*.trips' => 'required|integer|min:1'
            ]);

            // Mettre à jour l'approvisionnement (pas de product_id car il est dans les détails)
            // $supply->update([]);  // Rien à mettre à jour au niveau de l'approvisionnement principal

            // Mettre à jour les détails des villes
            $supply->cities()->detach();
            foreach ($validated['cities'] as $cityData) {
                $supply->cities()->attach($cityData['id'], [
                    'quantity' => $cityData['quantity'],
                    'price' => $cityData['price'],
                    'vehicle_id' => $cityData['vehicle_id'],
                    'driver_id' => $cityData['driver_id'],
                    'trips' => $cityData['trips']
                ]);
            }

            Log::info('Approvisionnement mis à jour avec succès', [
                'supply_id' => $supply->id
            ]);

            return redirect()->route('accountant.supplies.index')
                ->with('success', 'Approvisionnement mis à jour avec succès');

        } catch (\Exception $e) {
            Log::error('Erreur lors de la mise à jour de l\'approvisionnement', [
                'error' => $e->getMessage(),
                'supply_id' => $supply->id,
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->withInput()
                ->with('error', 'Une erreur est survenue lors de la mise à jour de l\'approvisionnement');
        }
    }

    /**
     * Supprime un approvisionnement
     */
    public function destroy(Supply $supply)
    {
        try {
            Log::info('Début de la suppression de l\'approvisionnement', [
                'supply_id' => $supply->id
            ]);

            $supply->cities()->detach();
            $supply->delete();

            Log::info('Approvisionnement supprimé avec succès');

            return redirect()->route('accountant.supplies.index')
                ->with('success', 'Approvisionnement supprimé avec succès');

        } catch (\Exception $e) {
            Log::error('Erreur lors de la suppression de l\'approvisionnement', [
                'error' => $e->getMessage(),
                'supply_id' => $supply->id,
                'trace' => $e->getTraceAsString()
            ]);

            return back()->with('error', 'Une erreur est survenue lors de la suppression de l\'approvisionnement');
        }
    }

    /**
     * Valider un approvisionnement.
     */
    public function validateSupply(Supply $supply)
    {
        try {
            Log::info('Début de la validation de l\'approvisionnement', ['supply_id' => $supply->id]);
            
            DB::beginTransaction();

            if ($supply->status !== 'pending') {
                throw new \Exception('Cet approvisionnement ne peut plus être validé.');
            }

            // Charger les relations nécessaires
            $supply->load(['cities.product']);
            Log::info('Relations chargées');

            // Mise à jour du statut de l'approvisionnement
            $supply->status = 'validated';
            $supply->validator_id = auth()->id();
            $supply->validated_at = now();
            $supply->save();
            Log::info('Statut de l\'approvisionnement mis à jour');

            // Mise à jour des stocks
            foreach ($supply->cities as $city) {
                Log::info('Mise à jour du stock pour le produit', [
                    'product_id' => $city->product_id,
                    'quantity' => $city->quantity
                ]);
                
                $product = Product::find($city->product_id);
                if ($product) {
                    $product->stock_quantity = $product->stock_quantity + $city->quantity;
                    $product->save();
                    
                    Log::info('Stock mis à jour', [
                        'product_id' => $product->id,
                        'old_stock' => $product->stock_quantity - $city->quantity,
                        'added_quantity' => $city->quantity,
                        'new_stock' => $product->stock_quantity
                    ]);
                }
            }

            DB::commit();
            Log::info('Validation de l\'approvisionnement terminée avec succès');

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'L\'approvisionnement a été validé avec succès.'
                ]);
            }

            return redirect()
                ->route('accountant.supplies.index')
                ->with('success', 'L\'approvisionnement a été validé avec succès.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la validation de l\'approvisionnement : ' . $e->getMessage());
            Log::error('Stack trace : ' . $e->getTraceAsString());
            
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Une erreur est survenue lors de la validation : ' . $e->getMessage()
                ], 422);
            }
            
            return redirect()
                ->route('accountant.supplies.index')
                ->with('error', 'Une erreur est survenue lors de la validation : ' . $e->getMessage());
        }
    }

    /**
     * Rejeter un approvisionnement.
     */
    public function rejectSupply(Request $request, Supply $supply)
    {
        try {
            Log::info('Début du rejet de l\'approvisionnement', ['supply_id' => $supply->id]);
            
            DB::beginTransaction();

            if ($supply->status !== 'pending') {
                throw new \Exception('Cet approvisionnement ne peut plus être rejeté.');
            }

            $request->validate([
                'rejection_reason' => 'required|string|max:255'
            ]);

            $supply->status = 'rejected';
            $supply->rejection_reason = $request->rejection_reason;
            $supply->validator_id = auth()->id();
            $supply->validated_at = now();
            $supply->save();

            DB::commit();
            Log::info('Rejet de l\'approvisionnement terminé avec succès');

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'L\'approvisionnement a été rejeté avec succès.'
                ]);
            }

            return redirect()
                ->route('accountant.supplies.index')
                ->with('success', 'L\'approvisionnement a été rejeté avec succès.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors du rejet de l\'approvisionnement : ' . $e->getMessage());
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Une erreur est survenue lors du rejet : ' . $e->getMessage()
                ], 422);
            }

            return redirect()
                ->route('accountant.supplies.index')
                ->with('error', 'Une erreur est survenue lors du rejet : ' . $e->getMessage());
        }
    }
}
